---
import { getCollection } from 'astro:content';
import LazyImage from './LazyImage.astro';
import Countdown from '../components/Countdown.astro';

// Get the home page content
const homePage = await getCollection('pages', ({ id }) => id === 'home.md');
const homeData = homePage[0].data;

// Get the latest news
const allNews = await getCollection('news');
const latestNews = allNews.sort((a, b) => b.data.date.getTime() - a.data.date.getTime()).slice(0, 3);

// Get the teams
const allTeams = await getCollection('teams');

// Get the sponsors
const allSponsors = await getCollection('sponsors');

const events = await getCollection('countdowns');
const now = Date.now();

const nextEvent = events
.filter((e) => e.data.active && new Date(e.data.date).getTime() > now)
.sort(
(a, b) =>
new Date(a.data.date).getTime() - new Date(b.data.date).getTime()
)[0];

---

<script src="../scripts/scrollAnimations.js"></script>

<div class="w-full">
  <!-- Hero Section -->
  <section class="relative min-h-[92.5dvh] text-white py-20 md:py-32 overflow-hidden">
    <!-- Diagonal overlay -->
    <div class="absolute inset-0 w-full h-full diagonal-split"></div>

    <!-- Background image -->
    <div class="absolute inset-1 w-full h-full opacity-90 bg-center transform scale-105 backdrop-blur-sm"
      style={`background-image: url('${homeData.heroImage}'); background-size: cover; background-position: center; filter: blur(5px);`}>
    </div>

    <!-- Content -->
    <div class="container mx-auto px-4 relative z-10">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="max-w-3xl">
          <div class="inline-block bg-usc-secondary px-4 py-1 rounded-full text-sm font-bold mb-6 fade-in">
            Willkommen beim USC Perchtoldsdorf
          </div>
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight fade-in"
            style="animation-delay: 200ms">
            {homeData.heroTitle}
          </h1>
          <p class="text-xl mb-8 text-white/90 slide-in-left" style="animation-delay: 400ms">
            {homeData.heroSubtitle}
          </p>
          <div class="flex flex-wrap gap-4 slide-in-left" style="animation-delay: 600ms">
            <a href="/verein"
              class="btn btn-lg bg-white text-usc-primary font-bold hover:bg-gray-100 shadow-lg hover:shadow-xl">
              <span>Über uns</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </a>
            <a href="/mannschaften"
              class="btn btn-lg bg-usc-secondary text-white font-bold hover:bg-usc-secondary-dark shadow-lg hover:shadow-xl">
              <span>Unsere Mannschaften</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </a>
          </div>
        </div>

        <!-- Hero stats/highlights -->
        <div class="hidden lg:block">
          <div class="grid grid-cols-2 gap-6">
            <div class="bg-white/10 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-white/20 scale-in"
              style="animation-delay: 300ms">
              <div class="text-4xl font-bold text-white mb-2">{homeData.jahreErfahrung}</div>
              <div class="text-lg text-white/80">Jahre Tradition</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-white/20 scale-in"
              style="animation-delay: 500ms">
              <div class="text-4xl font-bold text-white mb-2">{homeData.teamCount}</div>
              <div class="text-lg text-white/80">Mannschaften</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-white/20 scale-in"
              style="animation-delay: 700ms">
              <div class="text-4xl font-bold text-white mb-2">{homeData.activePlayerCount}</div>
              <div class="text-lg text-white/80">Aktive Spieler</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-white/20 scale-in"
              style="animation-delay: 900ms">
              <div class="text-4xl font-bold text-white mb-2">1</div>
              <div class="text-lg text-white/80">Gemeinschaft</div>
            </div>
          </div>
        </div>
      </div>
      <!-- Event-Countdown Overlay -->
      {nextEvent && (
      <Countdown date={nextEvent.data.date} title={nextEvent.data.title} description={nextEvent.data.description} />
      )}
    </div>
  </section>

  <!-- Current Update Widget -->
  <section class="relative z-20 flex justify-center mt-16">
    <div
      class="relative bg-gradient-to-br from-yellow-200 via-gray-200 to-red-300 rounded-xl shadow-lg px-8 py-6 flex-col md:flex-col items-center gap-6 animate-bounce-slow">
      <div class="flex-grow-1 mb-2">
        <img src="/uploads/images/static/usc-wir-ueber-ich.jpg" alt="Danke Illustration"
          class="w-full h-64 object-contain drop-shadow-lg" loading="lazy" />
      </div>
    </div>
    <style>
      .animate-pop-in {
        animation: pop-in 0.7s cubic-bezier(.68, -0.55, .27, 1.55) both;
      }

      @keyframes pop-in {
        0% {
          transform: scale(0.7) translateY(40px);
          opacity: 0;
        }

        80% {
          transform: scale(1.05) translateY(-8px);
          opacity: 1;
        }

        100% {
          transform: scale(1) translateY(0);
        }
      }

      .animate-bounce-slow {
        animation: bounce-slow 2.5s infinite;
      }

      @keyframes bounce-slow {

        0%,
        100% {
          transform: translateY(0);
        }

        50% {
          transform: translateY(-12px);
        }
      }

      .animate-heartbeat {
        animation: heartbeat 1.2s infinite;
      }

      @keyframes heartbeat {

        0%,
        100% {
          transform: scale(1);
        }

        20% {
          transform: scale(1.2);
        }

        40% {
          transform: scale(0.95);
        }

        60% {
          transform: scale(1.1);
        }

        80% {
          transform: scale(0.98);
        }
      }
    </style>
  </section>

  <!-- Current Update Widget -->
  <section class="relative z-20 flex justify-center mt-16">
    <div
      class="relative bg-gradient-to-br from-gray-200 via-red-300 to-yellow-200 rounded-xl shadow-lg px-8 py-6 flex flex-col md:flex-row items-center gap-6 animate-bounce-slow">
      <div class="flex-shrink-0">
        <img src="/uploads/images/static/usc-danke.png" alt="Danke Illustration"
          class="w-128 h-64 object-contain drop-shadow-lg" loading="lazy" />
      </div>
      <div>
        <h3 class="text-2xl md:text-3xl font-bold text-red-700 mb-2 drop-shadow">USC Perchtoldsdorf Top 10!</h3>
        <p class="text-lg md:text-xl font-semibold text-gray-900 mb-1">
          Der USC Perchtoldsdorf hat es bei der <span class="text-red-700 font-bold">österreichweiten ÖFB Topklub Wahl
            2024/25</span> auf den <span class="text-yellow-600 font-bold">10. Platz</span> geschafft!
        </p>
        <p class="text-base md:text-lg text-gray-800 font-medium">
          <span class="inline-block animate-heartbeat">❤️</span> Vielen herzlichen Dank an alle, die uns gewählt haben!
        </p>
      </div>
    </div>
    <style>
      .animate-pop-in {
        animation: pop-in 0.7s cubic-bezier(.68, -0.55, .27, 1.55) both;
      }

      @keyframes pop-in {
        0% {
          transform: scale(0.7) translateY(40px);
          opacity: 0;
        }

        80% {
          transform: scale(1.05) translateY(-8px);
          opacity: 1;
        }

        100% {
          transform: scale(1) translateY(0);
        }
      }

      .animate-bounce-slow {
        animation: bounce-slow 2.5s infinite;
      }

      @keyframes bounce-slow {

        0%,
        100% {
          transform: translateY(0);
        }

        50% {
          transform: translateY(-12px);
        }
      }

      .animate-heartbeat {
        animation: heartbeat 1.2s infinite;
      }

      @keyframes heartbeat {

        0%,
        100% {
          transform: scale(1);
        }

        20% {
          transform: scale(1.2);
        }

        40% {
          transform: scale(0.95);
        }

        60% {
          transform: scale(1.1);
        }

        80% {
          transform: scale(0.98);
        }
      }
    </style>
  </section>

  <!-- News Section -->
  <section class="section-padding relative overflow-hidden">
    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-100">
      <div class="football-pattern w-full h-full"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
      <div class="text-center mb-16">
        <h2 class="section-title animate-on-scroll" data-animation="fade-in">Aktuelle News</h2>
        <p class="text-gray-600 max-w-3xl mx-auto animate-on-scroll" data-animation="fade-in"
          style="animation-delay: 200ms">
          Bleiben Sie auf dem Laufenden mit den neuesten Nachrichten, Spielberichten und Veranstaltungen des USC
          Perchtoldsdorf.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        {latestNews.map((news, index) => (
        <div class="card shadow-hover animate-on-scroll" data-animation="slide-up" style={`animation-delay: ${index *
          150}ms`}>
          <a href={`/news/${news.id}`} class="card-link">
            <div class="card-header h-48">
              {news.data.image ? (
              <LazyImage src={news.data.image} alt={news.data.title} class="card-img" />
              ) : (
              <div class="h-full w-full bg-gray-300 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7h1m0 0h1m0 0h1m0 0h1m0 0h1m-5 10h1m0 0h1m0 0h1m0 0h1m0 0h1" />
                </svg>
              </div>
              )}

              <!-- Date badge -->
              <div
                class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-usc-primary text-sm font-bold py-1 px-3 rounded-full shadow-md">
                {news.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'short', day: 'numeric'})}
              </div>
            </div>

            <div class="card-body">
              <!-- Category badge if available -->
              {news.data.category && (
              <div class="inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full mb-2">
                {news.data.category}
              </div>
              )}

              <h3 class="card-title text-gray-800 hover:text-usc-primary transition-colors">{news.data.title}</h3>
              <p class="card-text mb-4 line-clamp-3">{news.data.excerpt}</p>

              <div class="flex items-center text-usc-primary font-medium">
                <span>Weiterlesen</span>
                <svg xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 ml-1 transform transition-transform group-hover:translate-x-1" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </a>
        </div>
        ))}
      </div>

      <div class="text-center mt-12 animate-on-scroll" data-animation="fade-in">
        <a href="/news" class="btn btn-primary btn-lg inline-flex items-center">
          <span>Alle News anzeigen</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        </a>
      </div>
    </div>
  </section>

<!-- USC Spritzerturnier -->
<section class="section-padding bg-white bg-opacity-50 relative overflow-hidden">
  <div class="container mx-auto px-4 relative z-10">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8 items-center">
      <div class="md:col-span-1 animate-on-scroll" data-animation="slide-in-left">
        <img 
          src="/uploads/images/static/usc-spritzer-turnier.jpg" 
          alt="USC Spritzerturnier" 
          class="rounded-lg shadow-lg w-full h-auto object-cover"
          loading="lazy"
        />
      </div>
      <div class="md:col-span-3 text-center">
        <h2 class="section-title animate-on-scroll" data-animation="fade-in">USCP Spritzerturnier II.</h2>
        <p class="text-gray-600 animate-on-scroll" data-animation="fade-in" style="animation-delay: 200ms">
          Das zweite USCP Spritzerturnier startet am 21. Juli 2025 um 10:00 Uhr in der Höhenstraße 15! Ein Fußballturnier der besonderen Art, bei dem der Spaß im Vordergrund steht. Das Nenngeld beträgt 100€ pro Team. Anmeldungen nehmen wir über Instagram @usc_perchtoldsdorf_1921 entgegen. Für das leibliche Wohl ist bestens gesorgt - von Spritzern bis zu Grillspezialitäten!
        </p>
        <div class="mt-4 animate-on-scroll" data-animation="fade-in" style="animation-delay: 400ms">
          <a href="https://www.instagram.com/usc_perchtoldsdorf_1921" 
             target="_blank" 
             rel="noopener noreferrer" 
             class="inline-flex items-center text-usc-primary hover:text-usc-secondary transition-colors">
            <span>Zur Anmeldung auf Instagram</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

  <!-- Coppa Mödling -->
  <section class="section-padding relative overflow-hidden">
    <div class="container mx-auto px-4 relative z-10">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 items-center">
        <div class="md:col-span-3 text-center">
          <h2 class="section-title animate-on-scroll" data-animation="fade-in">Coppa Mödling</h2>
          <p class="text-gray-600 animate-on-scroll" data-animation="fade-in" style="animation-delay: 200ms">
            Der USC Perchtoldsdorf veranstaltet am 23. und 24. August 2025 erstmals die "COPPA MÖDLING". Auf der grünen Sportanlage in Perchtoldsdorf werden an den beiden Tagen Turniere von der U8 (Jhg. 2018) bis zur U12 (Jhg. 2014) gespielt!
          </p>
        </div>
        <div class="md:col-span-1 animate-on-scroll" data-animation="slide-in-right">
          <img 
            src="/uploads/images/static/usc-coppa-moedling.jpg" 
            alt="Coppa Mödling" 
            class="rounded-lg shadow-lg w-full h-auto object-cover"
            loading="lazy"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Teams Section -->
  <section class="section-padding bg-white relative overflow-hidden">
    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="field-pattern w-full h-full"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
      <div class="text-center mb-16">
        <h2 class="section-title animate-on-scroll" data-animation="fade-in">Unsere Mannschaften</h2>
        <p class="text-gray-600 max-w-3xl mx-auto animate-on-scroll" data-animation="fade-in"
          style="animation-delay: 200ms">
          Vom Nachwuchs bis zur Kampfmannschaft - entdecken Sie unsere Teams und lernen Sie die Spieler kennen.
        </p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        {allTeams.sort((a, b) => (a.data.order ?? 0) - (b.data.order ?? 0)).map((team, index) => (
        <div class="card shadow-hover group animate-on-scroll" data-animation="slide-up" style={`animation-delay:
          ${index * 150}ms`}>
          <a href={team.data.externalUrl ? team.data.externalUrl : `/mannschaften/${team.slug}`} class="card-link"
            {...(team.data.externalUrl ? { target: "_blank" , rel: "noopener" } : {})}>
            <div class="card-header h-48 relative overflow-hidden">
              {team.data.image ? (
              <LazyImage src={team.data.image} alt={team.data.title} class="card-img" />
              ) : (
              <div class="h-full w-full bg-gray-300 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              )}

              <!-- Overlay -->
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-70 transition-opacity group-hover:opacity-90">
              </div>

              <!-- Category badge if available -->
              {team.data.category && (
              <div
                class="absolute top-4 left-4 bg-usc-primary/90 text-white text-xs font-bold py-1 px-3 rounded-full shadow-md">
                {team.data.category}
              </div>
              )}

              <!-- External link indicator -->
              {team.data.externalUrl && (
              <div
                class="absolute top-4 right-4 bg-white/90 text-usc-primary text-xs font-bold py-1 px-3 rounded-full shadow-md flex items-center">
                <span>Extern</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </div>
              )}
            </div>

            <div class="absolute bottom-0 left-0 right-0 p-6 text-white z-10">
              <h3 class="text-xl font-bold mb-2 group-hover:text-usc-secondary transition-colors">{team.data.title}</h3>
              <div class="flex items-center text-white/90 font-medium">
                <span>Ansehen</span>
                <svg xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 ml-1 transform transition-transform group-hover:translate-x-1" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </a>
        </div>
        ))}
      </div>

      <div class="text-center mt-12 animate-on-scroll" data-animation="fade-in">
        <a href="/mannschaften" class="btn btn-outline btn-outline-primary btn-lg inline-flex items-center">
          <span>Alle Mannschaften anzeigen</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        </a>
      </div>
    </div>
  </section>

  <!-- Sponsors Section -->
  <section class="section-padding relative overflow-hidden">
    <!-- Background pattern -->
    <div class="absolute inset-0">
      <div class="football-pattern w-full h-full"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
      <div class="text-center mb-16">
        <h2 class="section-title animate-on-scroll" data-animation="fade-in">Unsere Partner & Sponsoren</h2>
        <p class="text-gray-600 max-w-3xl mx-auto animate-on-scroll" data-animation="fade-in"
          style="animation-delay: 200ms">
          Wir danken unseren Sponsoren für ihre Unterstützung. Ohne sie wäre unsere Arbeit nicht möglich.
        </p>
      </div>

      <!-- Main sponsors with larger display -->
      <div class="mb-12">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {allSponsors
          .sort((a, b) => (a.data.order || 999) - (b.data.order || 999))
          .map((sponsor, index) => {if (index < 9) return (
          <div class="bg-white rounded-lg shadow-lg p-6 flex flex-col items-center justify-center animate-on-scroll"
            data-animation="scale-in" style={`animation-delay: ${index * 150}ms`}>
            {sponsor.data.logo ? (
            <a href={sponsor.data.website} target="_blank" rel="noopener noreferrer" class="block w-full mb-4 flex justify-center">
              <LazyImage src={sponsor.data.logo} alt={sponsor.data.title} class="sponsor-logo" />
            </a>
            ) : (
            <div class="text-2xl font-bold text-center mb-4">{sponsor.data.title}</div>
            )}
            {sponsor.data.logo ? sponsor.data.title : sponsor.data.description && (
            <p class="text-gray-600 text-center text-sm">{sponsor.data.logo ? sponsor.data.title :
              sponsor.data.description}</p>
            )}
            {sponsor.data.website && (
            <a href={sponsor.data.website} target="_blank" rel="noopener noreferrer"
              class="mt-4 text-usc-primary hover:text-usc-secondary transition-colors text-sm font-medium">
              Website besuchen
            </a>
            )}
          </div>
          )})
          }
        </div>
      </div>

      <div class="text-center mt-12 animate-on-scroll" data-animation="fade-in">
        <a href="/sponsoren" class="btn btn-outline btn-outline-primary btn-lg inline-flex items-center">
          <span>Alle Sponsoren anzeigen</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        </a>
      </div>

      <div class="text-center mt-16 animate-on-scroll" data-animation="fade-in">
        <div class="max-w-2xl mx-auto bg-white rounded-lg p-8 shadow-lg">
          <h3 class="text-2xl font-bold mb-4">Werden Sie Sponsor</h3>
          <p class="text-gray-600 mb-6">Unterstützen Sie den USC Perchtoldsdorf und profitieren Sie von attraktiven
            Werbemöglichkeiten. Wir bieten maßgeschneiderte Sponsoringpakete für jedes Budget.</p>
          <a href="/sponsoren#info" class="btn btn-primary btn-lg inline-flex items-center">
            <span>Mehr erfahren</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section
    class="py-20 md:py-32 bg-gradient-to-r from-usc-secondary to-usc-secondary-dark text-white relative overflow-hidden">
    <!-- Diagonal overlay -->
    <div class="absolute inset-0 w-full h-full diagonal-split"></div>

    <!-- Soccer ball pattern overlay -->
    <div class="absolute inset-0 w-full h-full opacity-5 football-pattern"></div>

    <!-- Background image -->
    <div class="absolute inset-0 w-full h-full opacity-20 bg-cover bg-center transform scale-105"
      style="background-image: url('/uploads/images/backgrounds/soccer-stock-image.jpg');"></div>

    <!-- Content -->
    <div class="container mx-auto px-4 text-center relative z-10">
      <div class="max-w-3xl mx-auto">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 animate-on-scroll" data-animation="fade-in">
          {homeData.ctaText}
        </h2>
        <p class="text-xl mb-10 animate-on-scroll" data-animation="slide-up" style="animation-delay: 200ms">
          {homeData.ctaSubtext}
        </p>
        <div class="animate-on-scroll" data-animation="bounce-in" style="animation-delay: 400ms">
          <a href="/kontakt"
            class="btn btn-lg bg-usc-secondary text-usc-primary font-bold hover:text-white shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
            <span>Kontakt aufnehmen</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </a>
        </div>
      </div>
    </div>

    <!-- Wave divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden leading-none transform rotate-180">
      <svg class="relative block w-full h-12 md:h-16" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1200 120" preserveAspectRatio="none">
        <path
          d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
          class="fill-white"></path>
      </svg>
    </div>
  </section>
</div>